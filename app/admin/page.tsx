'use client'

import { useState, useEffect } from 'react'
import { UserTable } from './components/user-table'
import { AdminActions } from './components/admin-actions'
import { getAdminUsers, getAdminStats, type AdminUser, type AdminStats } from '@/lib/admin'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Button } from '@/components/ui/button'
import { Search, RefreshCw } from 'lucide-react'
import { toast } from 'sonner'

export default function AdminPage() {
  const [users, setUsers] = useState<AdminUser[]>([])
  const [stats, setStats] = useState<AdminStats>({
    totalUsers: 0,
    newUsers: 0,
    contactedUsers: 0,
    convertedUsers: 0,
    totalRevenue: 0
  })
  const [loading, setLoading] = useState(true)
  const [selectedUsers, setSelectedUsers] = useState<string[]>([])
  
  // Filters and pagination
  const [search, setSearch] = useState('')
  const [contactFilter, setContactFilter] = useState('all')
  const [purchaseFilter, setPurchaseFilter] = useState('all')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalUsers, setTotalUsers] = useState(0)
  const [refreshing, setRefreshing] = useState(false)

  const USERS_PER_PAGE = 50

  // Load data
  const loadData = async (showRefreshing = false) => {
    try {
      if (showRefreshing) setRefreshing(true)
      else setLoading(true)

      const [usersResult, statsResult] = await Promise.all([
        getAdminUsers(
          currentPage,
          USERS_PER_PAGE,
          search || undefined,
          contactFilter === 'all' ? undefined : contactFilter,
          purchaseFilter === 'all' ? undefined : purchaseFilter
        ),
        getAdminStats()
      ])

      setUsers(usersResult.users)
      setTotalUsers(usersResult.total)
      setStats(statsResult)
    } catch (error) {
      console.error('Error loading admin data:', error)
      toast.error('Failed to load data')
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  // Initial load
  useEffect(() => {
    loadData()
  }, [currentPage, search, contactFilter, purchaseFilter])

  // Handle search with debounce
  useEffect(() => {
    const timer = setTimeout(() => {
      setCurrentPage(1) // Reset to first page on search
    }, 300)

    return () => clearTimeout(timer)
  }, [search])

  // Handle filter changes
  const handleFilterChange = () => {
    setCurrentPage(1)
    setSelectedUsers([])
  }

  // Handle user selection
  const handleUserSelect = (userId: string, selected: boolean) => {
    if (selected) {
      setSelectedUsers(prev => [...prev, userId])
    } else {
      setSelectedUsers(prev => prev.filter(id => id !== userId))
    }
  }

  const handleSelectAll = (selected: boolean) => {
    if (selected) {
      setSelectedUsers(users.map(user => user.id))
    } else {
      setSelectedUsers([])
    }
  }

  // Handle refresh
  const handleRefresh = () => {
    loadData(true)
  }

  // Calculate pagination
  const totalPages = Math.ceil(totalUsers / USERS_PER_PAGE)

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card className="bg-card border-primary/20">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-400">Total Users</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-mono font-bold text-primary">
              {stats.totalUsers.toLocaleString()}
            </div>
          </CardContent>
        </Card>

        <Card className="bg-card border-primary/20">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-400">New (7 days)</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-mono font-bold text-blue-400">
              {stats.newUsers.toLocaleString()}
            </div>
          </CardContent>
        </Card>

        <Card className="bg-card border-primary/20">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-400">Contacted</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-mono font-bold text-yellow-400">
              {stats.contactedUsers.toLocaleString()}
            </div>
          </CardContent>
        </Card>

        <Card className="bg-card border-primary/20">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-400">Converted</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-mono font-bold text-green-400">
              {stats.convertedUsers.toLocaleString()}
            </div>
          </CardContent>
        </Card>

        <Card className="bg-card border-primary/20">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-400">Revenue</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-mono font-bold text-primary">
              ${(stats.totalRevenue / 100).toLocaleString()}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Actions */}
      <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
        <div className="flex flex-col sm:flex-row gap-4 flex-1">
          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search users..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="pl-10 bg-black border-primary/30 text-white rounded-none"
            />
          </div>

          {/* Filters */}
          <div className="flex gap-2">
            <Select value={contactFilter} onValueChange={(value) => {
              setContactFilter(value)
              handleFilterChange()
            }}>
              <SelectTrigger className="w-40 bg-black border-primary/30 text-white rounded-none">
                <SelectValue placeholder="Contact Status" />
              </SelectTrigger>
              <SelectContent className="bg-black border-primary/30">
                <SelectItem value="all">All Contacts</SelectItem>
                <SelectItem value="new">New</SelectItem>
                <SelectItem value="contacted">Contacted</SelectItem>
                <SelectItem value="interested">Interested</SelectItem>
                <SelectItem value="converted">Converted</SelectItem>
                <SelectItem value="not_interested">Not Interested</SelectItem>
              </SelectContent>
            </Select>

            <Select value={purchaseFilter} onValueChange={(value) => {
              setPurchaseFilter(value)
              handleFilterChange()
            }}>
              <SelectTrigger className="w-40 bg-black border-primary/30 text-white rounded-none">
                <SelectValue placeholder="Purchases" />
              </SelectTrigger>
              <SelectContent className="bg-black border-primary/30">
                <SelectItem value="all">All Users</SelectItem>
                <SelectItem value="has_purchases">Has Purchases</SelectItem>
                <SelectItem value="no_purchases">No Purchases</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Actions */}
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
            className="rounded-none"
          >
            <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          
          <AdminActions
            selectedUsers={selectedUsers}
            users={users}
            onActionComplete={() => {
              setSelectedUsers([])
              loadData(true)
            }}
          />
        </div>
      </div>

      {/* User Table */}
      <UserTable
        users={users}
        loading={loading}
        selectedUsers={selectedUsers}
        onUserSelect={handleUserSelect}
        onSelectAll={handleSelectAll}
        onUserUpdate={() => loadData(true)}
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={setCurrentPage}
      />
    </div>
  )
}
