'use client'

import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON><PERSON>nt, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { type AdminUser, updateUserContactStatus, updateUserProfile, CONTACT_STATUSES, PURCHASE_STATUSES } from '@/lib/admin'
import { toast } from 'sonner'
import { Loader2, Plus, X } from 'lucide-react'

interface UserEditModalProps {
  user: AdminUser
  isOpen: boolean
  onClose: () => void
  onSave: () => void
}

export function UserEditModal({ user, isOpen, onClose, onSave }: UserEditModalProps) {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: user.name || '',
    phone: user.phone || '',
    contactStatus: user.contactStatus,
    notes: user.notes || '',
  })

  const handleSave = async () => {
    try {
      setLoading(true)

      // Update contact status and notes
      const contactResult = await updateUserContactStatus(
        user.id,
        formData.contactStatus,
        formData.notes
      )

      if (!contactResult.success) {
        toast.error(contactResult.error || 'Failed to update contact info')
        return
      }

      // Update profile data if changed
      const profileUpdates: any = {}
      if (formData.name !== user.name) profileUpdates.name = formData.name
      if (formData.phone !== user.phone) profileUpdates.phone = formData.phone

      if (Object.keys(profileUpdates).length > 0) {
        const profileResult = await updateUserProfile(user.id, profileUpdates)
        if (!profileResult.success) {
          toast.error(profileResult.error || 'Failed to update profile')
          return
        }
      }

      toast.success('User updated successfully')
      onSave()
    } catch (error) {
      console.error('Error updating user:', error)
      toast.error('Failed to update user')
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (cents: number) => {
    return `$${(cents / 100).toLocaleString()}`
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  const getStatusBadge = (status: string, type: 'contact' | 'purchase') => {
    const statuses = type === 'contact' ? CONTACT_STATUSES : PURCHASE_STATUSES
    const statusConfig = statuses.find(s => s.value === status)
    if (!statusConfig) return status

    return (
      <Badge 
        variant="secondary" 
        className={`${statusConfig.color} text-white text-xs rounded-none`}
      >
        {statusConfig.label}
      </Badge>
    )
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto bg-black border-primary/30 rounded-none">
        <DialogHeader>
          <DialogTitle className="text-primary font-mono uppercase tracking-wider">
            Edit User: {user.email}
          </DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Left Column - Edit Form */}
          <div className="space-y-6">
            <Card className="bg-card border-primary/20">
              <CardHeader>
                <CardTitle className="text-sm font-mono text-primary uppercase">
                  User Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email" className="text-gray-400 text-xs uppercase font-mono">
                    Email
                  </Label>
                  <Input
                    id="email"
                    value={user.email}
                    disabled
                    className="bg-gray-900 border-primary/30 text-gray-400 rounded-none"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="name" className="text-gray-400 text-xs uppercase font-mono">
                    Name
                  </Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    className="bg-black border-primary/30 text-white rounded-none"
                    placeholder="Enter name"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone" className="text-gray-400 text-xs uppercase font-mono">
                    Phone
                  </Label>
                  <Input
                    id="phone"
                    value={formData.phone}
                    onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                    className="bg-black border-primary/30 text-white rounded-none"
                    placeholder="Enter phone number"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-400">Country:</span>
                    <div className="text-white">{user.country || 'Unknown'}</div>
                  </div>
                  <div>
                    <span className="text-gray-400">Joined:</span>
                    <div className="text-white">{formatDate(user.joinDate)}</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-card border-primary/20">
              <CardHeader>
                <CardTitle className="text-sm font-mono text-primary uppercase">
                  Contact Management
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="contactStatus" className="text-gray-400 text-xs uppercase font-mono">
                    Contact Status
                  </Label>
                  <Select
                    value={formData.contactStatus}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, contactStatus: value }))}
                  >
                    <SelectTrigger className="bg-black border-primary/30 text-white rounded-none">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-black border-primary/30">
                      {CONTACT_STATUSES.map((status) => (
                        <SelectItem key={status.value} value={status.value}>
                          <div className="flex items-center space-x-2">
                            <div className={`w-2 h-2 rounded-full ${status.color}`} />
                            <span>{status.label}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {user.lastContacted && (
                  <div className="text-sm">
                    <span className="text-gray-400">Last Contacted:</span>
                    <div className="text-white">{formatDate(user.lastContacted)}</div>
                  </div>
                )}

                <div className="space-y-2">
                  <Label htmlFor="notes" className="text-gray-400 text-xs uppercase font-mono">
                    Admin Notes
                  </Label>
                  <Textarea
                    id="notes"
                    value={formData.notes}
                    onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                    className="bg-black border-primary/30 text-white rounded-none min-h-20"
                    placeholder="Add private admin notes..."
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Column - View Only Data */}
          <div className="space-y-6">
            <Card className="bg-card border-primary/20">
              <CardHeader>
                <CardTitle className="text-sm font-mono text-primary uppercase">
                  Selected Products ({user.productCount})
                </CardTitle>
              </CardHeader>
              <CardContent>
                {user.selectedProducts.length > 0 ? (
                  <div className="space-y-3">
                    {user.selectedProducts.map((product, idx) => (
                      <div key={idx} className="flex items-center justify-between p-3 bg-black/50 border border-primary/10 rounded-none">
                        <div>
                          <div className="font-medium text-white text-sm">{product.name}</div>
                          <div className="text-xs text-gray-400">
                            Selected {formatDate(product.selected_at)}
                          </div>
                        </div>
                        <div className="text-primary font-mono text-sm">
                          {product.price}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-gray-400 text-sm">No products selected</div>
                )}
              </CardContent>
            </Card>

            <Card className="bg-card border-primary/20">
              <CardHeader>
                <CardTitle className="text-sm font-mono text-primary uppercase">
                  Purchases ({user.purchaseCount})
                </CardTitle>
              </CardHeader>
              <CardContent>
                {user.purchases.length > 0 ? (
                  <div className="space-y-3">
                    {user.purchases.map((purchase, idx) => (
                      <div key={idx} className="p-3 bg-black/50 border border-primary/10 rounded-none">
                        <div className="flex items-center justify-between mb-2">
                          <div className="font-medium text-white text-sm">
                            {purchase.product_name}
                          </div>
                          <div className="text-green-400 font-mono text-sm">
                            {formatCurrency(purchase.amount_cents)}
                          </div>
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="text-xs text-gray-400">
                            {formatDate(purchase.purchased_at)}
                          </div>
                          {getStatusBadge(purchase.status, 'purchase')}
                        </div>
                      </div>
                    ))}
                    <Separator className="bg-primary/20" />
                    <div className="flex justify-between items-center pt-2">
                      <span className="text-gray-400 text-sm">Total Spent:</span>
                      <span className="text-green-400 font-mono font-semibold">
                        {formatCurrency(user.totalSpent)}
                      </span>
                    </div>
                  </div>
                ) : (
                  <div className="text-gray-400 text-sm">No purchases yet</div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-4 pt-6 border-t border-primary/20">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={loading}
            className="rounded-none"
          >
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={loading}
            className="rounded-none"
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Save Changes
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
