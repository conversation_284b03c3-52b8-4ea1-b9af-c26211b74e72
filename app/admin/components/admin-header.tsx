'use client'

import { useAuthContext } from '@/contexts/auth-context'
import { Button } from '@/components/ui/button'
import { LogOut } from 'lucide-react'
import { toast } from 'sonner'

export function AdminHeader() {
  const { user, signOut } = useAuthContext()

  const handleSignOut = async () => {
    try {
      await signOut()
      toast.success('Signed out successfully')
    } catch (error) {
      toast.error('Failed to sign out')
    }
  }

  return (
    <header className="border-b border-primary/20 bg-black/95 backdrop-blur-sm">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h1 className="text-xl font-mono font-semibold text-primary uppercase tracking-wider">
              Admin Dashboard
            </h1>
          </div>
          <div className="flex items-center space-x-4">
            {user && (
              <span className="text-sm text-gray-400">
                {user.email}
              </span>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={handleSignOut}
              className="text-gray-400 hover:text-primary rounded-none"
            >
              <LogOut className="h-4 w-4 mr-2" />
              Sign Out
            </Button>
            <a
              href="/"
              className="text-sm text-gray-400 hover:text-primary transition-colors"
            >
              ← Back to Site
            </a>
          </div>
        </div>
      </div>
    </header>
  )
}
