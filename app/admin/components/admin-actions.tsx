'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { <PERSON>alog, DialogContent, DialogHeader, <PERSON>alogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { type AdminUser, updateUserContactStatus, exportUsersToCSV, CONTACT_STATUSES } from '@/lib/admin'
import { toast } from 'sonner'
import { Download, Users, Loader2 } from 'lucide-react'

interface AdminActionsProps {
  selectedUsers: string[]
  users: AdminUser[]
  onActionComplete: () => void
}

export function AdminActions({ selectedUsers, users, onActionComplete }: AdminActionsProps) {
  const [bulkUpdateOpen, setBulkUpdateOpen] = useState(false)
  const [bulkLoading, setBulkLoading] = useState(false)
  const [exportLoading, setExportLoading] = useState(false)
  const [bulkStatus, setBulkStatus] = useState('')
  const [bulkNotes, setBulkNotes] = useState('')

  const selectedUserData = users.filter(user => selectedUsers.includes(user.id))

  const handleBulkUpdate = async () => {
    if (!bulkStatus || selectedUsers.length === 0) {
      toast.error('Please select users and a status')
      return
    }

    try {
      setBulkLoading(true)

      // Update all selected users
      const promises = selectedUsers.map(userId =>
        updateUserContactStatus(userId, bulkStatus, bulkNotes || undefined)
      )

      const results = await Promise.all(promises)
      const failures = results.filter(r => !r.success)

      if (failures.length === 0) {
        toast.success(`Updated ${selectedUsers.length} users successfully`)
      } else {
        toast.error(`Failed to update ${failures.length} users`)
      }

      setBulkUpdateOpen(false)
      setBulkStatus('')
      setBulkNotes('')
      onActionComplete()
    } catch (error) {
      console.error('Bulk update error:', error)
      toast.error('Failed to update users')
    } finally {
      setBulkLoading(false)
    }
  }

  const handleExportSelected = () => {
    if (selectedUsers.length === 0) {
      toast.error('Please select users to export')
      return
    }

    try {
      setExportLoading(true)
      const csvContent = exportUsersToCSV(selectedUserData)
      
      // Create and download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      
      link.setAttribute('href', url)
      link.setAttribute('download', `users_export_${new Date().toISOString().split('T')[0]}.csv`)
      link.style.visibility = 'hidden'
      
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      toast.success(`Exported ${selectedUsers.length} users`)
    } catch (error) {
      console.error('Export error:', error)
      toast.error('Failed to export users')
    } finally {
      setExportLoading(false)
    }
  }

  const handleExportAll = () => {
    if (users.length === 0) {
      toast.error('No users to export')
      return
    }

    try {
      setExportLoading(true)
      const csvContent = exportUsersToCSV(users)
      
      // Create and download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      
      link.setAttribute('href', url)
      link.setAttribute('download', `all_users_export_${new Date().toISOString().split('T')[0]}.csv`)
      link.style.visibility = 'hidden'
      
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      toast.success(`Exported ${users.length} users`)
    } catch (error) {
      console.error('Export error:', error)
      toast.error('Failed to export users')
    } finally {
      setExportLoading(false)
    }
  }

  return (
    <div className="flex gap-2">
      {/* Bulk Update */}
      {selectedUsers.length > 0 && (
        <Dialog open={bulkUpdateOpen} onOpenChange={setBulkUpdateOpen}>
          <DialogTrigger asChild>
            <Button variant="outline" size="sm" className="rounded-none">
              <Users className="h-4 w-4 mr-2" />
              Update ({selectedUsers.length})
            </Button>
          </DialogTrigger>
          <DialogContent className="bg-black border-primary/30 rounded-none">
            <DialogHeader>
              <DialogTitle className="text-primary font-mono uppercase tracking-wider">
                Bulk Update Users
              </DialogTitle>
            </DialogHeader>
            
            <div className="space-y-4">
              <div className="text-sm text-gray-400">
                Updating {selectedUsers.length} selected users
              </div>

              <div className="space-y-2">
                <Label htmlFor="bulkStatus" className="text-gray-400 text-xs uppercase font-mono">
                  Contact Status
                </Label>
                <Select value={bulkStatus} onValueChange={setBulkStatus}>
                  <SelectTrigger className="bg-black border-primary/30 text-white rounded-none">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent className="bg-black border-primary/30">
                    {CONTACT_STATUSES.map((status) => (
                      <SelectItem key={status.value} value={status.value}>
                        <div className="flex items-center space-x-2">
                          <div className={`w-2 h-2 rounded-full ${status.color}`} />
                          <span>{status.label}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="bulkNotes" className="text-gray-400 text-xs uppercase font-mono">
                  Notes (Optional)
                </Label>
                <Textarea
                  id="bulkNotes"
                  value={bulkNotes}
                  onChange={(e) => setBulkNotes(e.target.value)}
                  className="bg-black border-primary/30 text-white rounded-none"
                  placeholder="Add notes for all selected users..."
                  rows={3}
                />
              </div>

              <div className="flex justify-end space-x-2 pt-4">
                <Button
                  variant="outline"
                  onClick={() => setBulkUpdateOpen(false)}
                  disabled={bulkLoading}
                  className="rounded-none"
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleBulkUpdate}
                  disabled={bulkLoading || !bulkStatus}
                  className="rounded-none"
                >
                  {bulkLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Update Users
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}

      {/* Export Selected */}
      {selectedUsers.length > 0 && (
        <Button
          variant="outline"
          size="sm"
          onClick={handleExportSelected}
          disabled={exportLoading}
          className="rounded-none"
        >
          {exportLoading ? (
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <Download className="h-4 w-4 mr-2" />
          )}
          Export Selected
        </Button>
      )}

      {/* Export All */}
      <Button
        variant="outline"
        size="sm"
        onClick={handleExportAll}
        disabled={exportLoading}
        className="rounded-none"
      >
        {exportLoading ? (
          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
        ) : (
          <Download className="h-4 w-4 mr-2" />
        )}
        Export All
      </Button>
    </div>
  )
}
