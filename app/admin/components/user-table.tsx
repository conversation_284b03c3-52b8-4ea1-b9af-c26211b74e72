'use client'

import { useState } from 'react'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Checkbox } from '@/components/ui/checkbox'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card } from '@/components/ui/card'
import { UserEditModal } from './user-edit-modal'
import { type AdminUser, CONTACT_STATUSES } from '@/lib/admin'
import { Edit, ChevronLeft, ChevronRight } from 'lucide-react'
import { Skeleton } from '@/components/ui/skeleton'

interface UserTableProps {
  users: AdminUser[]
  loading: boolean
  selectedUsers: string[]
  onUserSelect: (userId: string, selected: boolean) => void
  onSelectAll: (selected: boolean) => void
  onUserUpdate: () => void
  currentPage: number
  totalPages: number
  onPageChange: (page: number) => void
}

export function UserTable({
  users,
  loading,
  selectedUsers,
  onUserSelect,
  onSelectAll,
  onUserUpdate,
  currentPage,
  totalPages,
  onPageChange
}: UserTableProps) {
  const [editingUser, setEditingUser] = useState<AdminUser | null>(null)

  const getContactStatusBadge = (status: string) => {
    const statusConfig = CONTACT_STATUSES.find(s => s.value === status)
    if (!statusConfig) return null

    return (
      <Badge 
        variant="secondary" 
        className={`${statusConfig.color} text-white text-xs rounded-none`}
      >
        {statusConfig.label}
      </Badge>
    )
  }

  const formatCurrency = (cents: number) => {
    return `$${(cents / 100).toLocaleString()}`
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  if (loading) {
    return (
      <Card className="bg-card border-primary/20">
        <div className="p-6">
          <div className="space-y-4">
            {[...Array(10)].map((_, i) => (
              <div key={i} className="flex items-center space-x-4">
                <Skeleton className="h-4 w-4" />
                <Skeleton className="h-4 w-48" />
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-20" />
              </div>
            ))}
          </div>
        </div>
      </Card>
    )
  }

  const allSelected = users.length > 0 && selectedUsers.length === users.length
  const someSelected = selectedUsers.length > 0 && selectedUsers.length < users.length

  return (
    <>
      <Card className="bg-card border-primary/20">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="border-primary/20 hover:bg-transparent">
                <TableHead className="w-12">
                  <Checkbox
                    checked={allSelected}
                    ref={(el) => {
                      if (el) el.indeterminate = someSelected
                    }}
                    onCheckedChange={(checked) => onSelectAll(!!checked)}
                    className="border-primary/30"
                  />
                </TableHead>
                <TableHead className="text-gray-400 font-mono text-xs uppercase tracking-wider">
                  User Info
                </TableHead>
                <TableHead className="text-gray-400 font-mono text-xs uppercase tracking-wider">
                  Products
                </TableHead>
                <TableHead className="text-gray-400 font-mono text-xs uppercase tracking-wider">
                  Purchases
                </TableHead>
                <TableHead className="text-gray-400 font-mono text-xs uppercase tracking-wider">
                  Contact
                </TableHead>
                <TableHead className="text-gray-400 font-mono text-xs uppercase tracking-wider">
                  Actions
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {users.map((user) => (
                <TableRow 
                  key={user.id} 
                  className="border-primary/10 hover:bg-primary/5 transition-colors"
                >
                  <TableCell>
                    <Checkbox
                      checked={selectedUsers.includes(user.id)}
                      onCheckedChange={(checked) => onUserSelect(user.id, !!checked)}
                      className="border-primary/30"
                    />
                  </TableCell>
                  
                  <TableCell>
                    <div className="space-y-1">
                      <div className="font-medium text-white text-sm">
                        {user.email}
                      </div>
                      {user.name && (
                        <div className="text-gray-400 text-xs">
                          {user.name}
                        </div>
                      )}
                      {user.phone && (
                        <div className="text-gray-400 text-xs">
                          {user.phone}
                        </div>
                      )}
                      <div className="text-gray-500 text-xs">
                        Joined {formatDate(user.joinDate)}
                        {user.country && ` • ${user.country}`}
                      </div>
                    </div>
                  </TableCell>

                  <TableCell>
                    <div className="space-y-1">
                      <div className="text-sm font-mono text-primary">
                        {user.productCount} selected
                      </div>
                      {user.selectedProducts.slice(0, 2).map((product, idx) => (
                        <Badge 
                          key={idx}
                          variant="outline" 
                          className="text-xs border-primary/30 text-gray-300 rounded-none"
                        >
                          {product.name}
                        </Badge>
                      ))}
                      {user.selectedProducts.length > 2 && (
                        <div className="text-xs text-gray-500">
                          +{user.selectedProducts.length - 2} more
                        </div>
                      )}
                    </div>
                  </TableCell>

                  <TableCell>
                    <div className="space-y-1">
                      <div className="text-sm font-mono text-primary">
                        {user.purchaseCount} purchases
                      </div>
                      <div className="text-sm font-mono text-green-400">
                        {formatCurrency(user.totalSpent)}
                      </div>
                      {user.purchases.slice(0, 1).map((purchase, idx) => (
                        <div key={idx} className="text-xs text-gray-400">
                          {purchase.product_name} • {purchase.status}
                        </div>
                      ))}
                    </div>
                  </TableCell>

                  <TableCell>
                    <div className="space-y-2">
                      {getContactStatusBadge(user.contactStatus)}
                      {user.lastContacted && (
                        <div className="text-xs text-gray-500">
                          Last: {formatDate(user.lastContacted)}
                        </div>
                      )}
                      {user.notes && (
                        <div className="text-xs text-gray-400 truncate max-w-32">
                          {user.notes}
                        </div>
                      )}
                    </div>
                  </TableCell>

                  <TableCell>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setEditingUser(user)}
                      className="h-8 w-8 p-0 hover:bg-primary/10 rounded-none"
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between px-6 py-4 border-t border-primary/20">
            <div className="text-sm text-gray-400">
              Page {currentPage} of {totalPages}
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onPageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className="rounded-none"
              >
                <ChevronLeft className="h-4 w-4" />
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onPageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="rounded-none"
              >
                Next
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}
      </Card>

      {/* Edit Modal */}
      {editingUser && (
        <UserEditModal
          user={editingUser}
          isOpen={true}
          onClose={() => setEditingUser(null)}
          onSave={() => {
            setEditingUser(null)
            onUserUpdate()
          }}
        />
      )}
    </>
  )
}
