import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Admin Dashboard - TradeForm',
  description: 'Admin dashboard for user management',
}

interface AdminLayoutProps {
  children: React.ReactNode
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  // Admin protection is handled by middleware

  return (
    <div className="min-h-screen bg-black">
      {/* Admin Header */}
      <header className="border-b border-primary/20 bg-black/95 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h1 className="text-xl font-mono font-semibold text-primary uppercase tracking-wider">
                Admin Dashboard
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <a
                href="/"
                className="text-sm text-gray-400 hover:text-primary transition-colors"
              >
                ← Back to Site
              </a>
            </div>
          </div>
        </div>
      </header>

      {/* Admin Content */}
      <main className="container mx-auto px-4 py-8">
        {children}
      </main>
    </div>
  )
}
