import type { Metadata } from 'next'
import { AdminHeader } from './components/admin-header'

export const metadata: Metadata = {
  title: 'Admin Dashboard - TradeForm',
  description: 'Admin dashboard for user management',
}

interface AdminLayoutProps {
  children: React.ReactNode
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  // Admin protection is handled by middleware

  return (
    <div className="min-h-screen bg-black">
      <AdminHeader />

      {/* Admin Content */}
      <main className="container mx-auto px-4 py-8">
        {children}
      </main>
    </div>
  )
}
