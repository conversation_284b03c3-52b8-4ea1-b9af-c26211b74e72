// Core sections
import { HeroSection } from "@/components/hero-section"
import { BeforeAfterSection } from "@/components/before-after-section"
import { MentorStorySection } from "@/components/mentor-story-section"
import { ProductStack } from "@/components/product-stack"
import { ProductHighlights } from "@/components/product-highlights"
import { CredibilitySection } from "@/components/credibility-section"
import { FaqSection } from "@/components/faq-section"
import { MoneyBackGuarantee } from "@/components/money-back-guarantee"
import { SocialContact } from "@/components/social-contact"

// Layout components
import { StickyHeader } from "@/components/sticky-header"
import { Footer } from "@/components/footer"
import { FloatingCTA } from "@/components/floating-cta"
import { SectionContainer } from "@/components/section-container"

// Analytics
import { PageViewTracker } from "@/components/analytics/page-view-tracker"
import { SectionViewTracker } from "@/components/analytics/section-view-tracker"

export default function HomePage() {

  return (
    <main className="relative">
      {/* Page Analytics Tracking */}
      <PageViewTracker page="home" />

      <StickyHeader />

      {/* Hero Section */}
      <SectionContainer id="hero" fullWidth={true}>
        <SectionViewTracker sectionId="hero" sectionName="Hero">
          <HeroSection />
        </SectionViewTracker>
      </SectionContainer>

      {/* Before/After Section */}
      <SectionContainer id="before-after" withGreenLine={true}>
        <SectionViewTracker sectionId="before-after" sectionName="Before/After">
          <BeforeAfterSection />
        </SectionViewTracker>
      </SectionContainer>



      {/* Product Stack */}
      <SectionContainer id="pricing" fullWidth={true} withGreenLine={true}>
        <SectionViewTracker sectionId="pricing" sectionName="Pricing">
          <ProductStack />
        </SectionViewTracker>
      </SectionContainer>

    {/* Mentor Story Section */}
    <SectionContainer id="mentor-story" fullWidth={true} withGreenLine={true}>
        <SectionViewTracker sectionId="mentor-story" sectionName="Mentor Story">
            <MentorStorySection/>
        </SectionViewTracker>
    </SectionContainer>

      {/* Product Highlights */}
      <SectionContainer id="features">
        <SectionViewTracker sectionId="features" sectionName="Features">
          <ProductHighlights />
        </SectionViewTracker>
      </SectionContainer>

      {/* Credibility Section */}
      <SectionContainer id="testimonials" fullWidth={true}>
        <SectionViewTracker sectionId="testimonials" sectionName="Testimonials">
          <CredibilitySection />
        </SectionViewTracker>
      </SectionContainer>

        {/* FAQ Section */}
        <SectionContainer id="faq" fullWidth={true} withGreenLine={true}>
            <SectionViewTracker sectionId="faq" sectionName="FAQ">
              <FaqSection />
            </SectionViewTracker>
        </SectionContainer>

      {/* Money Back Guarantee */}
      <SectionContainer id="guarantee">
        <SectionViewTracker sectionId="guarantee" sectionName="Guarantee">
          <MoneyBackGuarantee />
        </SectionViewTracker>
      </SectionContainer>

      {/* Social Contact */}
      <SectionContainer id="contact" withGreenLine={true}>
        <SectionViewTracker sectionId="contact" sectionName="Contact">
          <SocialContact />
        </SectionViewTracker>
      </SectionContainer>

      <Footer />
      <FloatingCTA />
    </main>
  )
}
