"use client"

import { useState, useEffect, useCallback } from 'react'
import { AuthService, type AuthUser, type AuthResult } from '@/lib/auth'
import { supabase } from '@/lib/supabase'
import type { AuthChangeEvent, Session } from '@supabase/supabase-js'

interface UseAuthReturn {
  user: AuthUser | null
  loading: boolean
  signUpWithEmail: (email: string, selectedProduct?: any) => Promise<AuthResult>
  signInWithPassword: (email: string, password: string) => Promise<AuthResult>
  signOut: () => Promise<void>
  updateProfile: (updates: any) => Promise<any>
}

/**
 * Authentication hook for managing user state and auth operations
 */
export function useAuth(): UseAuthReturn {
  const [user, setUser] = useState<AuthUser | null>(null)
  const [loading, setLoading] = useState(true)

  // Initialize auth state
  useEffect(() => {
    let mounted = true

    // Get initial session
    const getInitialSession = async () => {
      try {
        const currentUser = await AuthService.getCurrentUser()
        if (mounted) {
          setUser(currentUser)
          setLoading(false)
        }
      } catch (error) {
        console.error('Error getting initial session:', error)
        console.log(mounted)
        if (mounted) {
          setUser(null)
          setLoading(false)
        }
      }
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event: AuthChangeEvent, session: Session | null) => {
        if (mounted) {
          if (session?.user) {
            // User signed in - fetch full user data with profile
            try {
              const currentUser = await AuthService.getCurrentUser()
              setUser(currentUser)
            } catch (error) {
              setUser(null)
            }
          } else {
            // User signed out
            setUser(null)
          }
          setLoading(false)
        }
      }
    )

    return () => {
      mounted = false
      subscription.unsubscribe()
    }
  }, [])

  // Sign up with email
  const signUpWithEmail = useCallback(async (email: string, selectedProduct?: any): Promise<AuthResult> => {
    setLoading(true)
    try {
      const result = await AuthService.signUpWithEmail(email, selectedProduct)

      if (result.user) {
        setUser(result.user)
      }

      return result
    } catch (error) {
      return {
        user: null,
        error: {
          message: error instanceof Error ? error.message : 'Unknown error occurred',
          name: 'SignupError',
          status: 500
        } as any
      }
    } finally {
      setLoading(false)
    }
  }, [])

  // Sign in with password
  const signInWithPassword = useCallback(async (email: string, password: string): Promise<AuthResult> => {
    setLoading(true)
    try {
      const result = await AuthService.signInWithPassword(email, password)

      if (result.user) {
        setUser(result.user)
      }

      return result
    } catch (error) {
      return {
        user: null,
        error: {
          message: error instanceof Error ? error.message : 'Unknown error occurred',
          name: 'SignInError',
          status: 500
        } as any
      }
    } finally {
      setLoading(false)
    }
  }, [])

  // Sign out
  const signOut = useCallback(async () => {
    setLoading(true)
    try {
      await AuthService.signOut()
      setUser(null)
    } catch (error) {
      // Silent error handling
    } finally {
      setLoading(false)
    }
  }, [])

  // Update profile
  const updateProfile = useCallback(async (updates: any) => {
    if (!user) {
      throw new Error('No user logged in')
    }

    try {
      const result = await AuthService.updateProfile(user.id, updates)
      
      if (result.data) {
        // Update local user state with new profile data
        setUser(prev => prev ? { ...prev, profile: result.data } : null)
      }
      
      return result
    } catch (error) {
      console.error('Update profile error in hook:', error)
      throw error
    }
  }, [user])

  return {
    user,
    loading,
    signUpWithEmail,
    signInWithPassword,
    signOut,
    updateProfile
  }
}
