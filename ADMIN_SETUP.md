# Admin System Setup

## Overview
The admin system provides a simple, secure interface for managing users, tracking contacts, and updating purchase information.

## Features
- **User Management Table**: View all users with their contact info, selected products, and purchases
- **Contact Status Tracking**: Update contact status (New, Contacted, Interested, Converted, Not Interested)
- **Search & Filtering**: Search by email/name/phone, filter by contact status and purchase status
- **Bulk Operations**: Update multiple users at once
- **Export Functionality**: Export user data to CSV
- **Inline Editing**: Quick edit user information and contact status

## Setup Instructions

### 1. Environment Configuration
Add the following to your `.env.local` file:

```bash
# Admin Configuration
ADMIN_EMAILS=<EMAIL>,<EMAIL>
```

Replace with the actual email addresses that should have admin access.

### 2. Database Schema
The admin system uses the existing `profiles` table with the following structure:

- **User Info**: email, name, phone, geo (country)
- **Selected Products**: Array of products the user is interested in
- **Purchases**: Array of completed purchases with amounts and status
- **Contact Info**: Contact status, last contacted date, admin notes

### 3. Create Admin User Account
Since the system uses email/password authentication, you need to create an account for your admin email:

1. **Option A: Use Supabase Dashboard**
   - Go to your Supabase project dashboard
   - Navigate to Authentication > Users
   - Click "Add user" and create an account with your admin email

2. **Option B: Use the signup flow on your site**
   - Go to your main site and use the signup modal
   - Sign up with your admin email address
   - This will create the account automatically

### 4. Access the Admin Panel
1. Navigate to `/admin` in your browser
2. You'll be redirected to `/admin/login` if not authenticated
3. Sign in with your admin email and password
4. You'll be redirected to the admin dashboard

## Usage Guide

### User Table
- **Search**: Use the search bar to find users by email, name, or phone
- **Filter**: Filter by contact status or purchase status
- **Select**: Use checkboxes to select users for bulk operations
- **Edit**: Click the edit button to open the user details modal

### Contact Management
- **Status Updates**: Change contact status to track your outreach progress
- **Notes**: Add private admin notes for each user
- **Bulk Updates**: Select multiple users and update their status at once

### Export Data
- **Export Selected**: Export only the users you've selected
- **Export All**: Export all users (respects current filters)
- **CSV Format**: Includes all user data, contact info, and purchase history

### User Edit Modal
- **User Information**: Edit name and phone number
- **Contact Status**: Update contact status and add notes
- **View Products**: See all products the user has selected
- **View Purchases**: See purchase history and amounts

## Contact Status Workflow

1. **New** - User just signed up
2. **Contacted** - You've reached out to them
3. **Interested** - They've shown interest
4. **Converted** - They've made a purchase
5. **Not Interested** - They're not interested

## Security Features

- **Middleware Protection**: Admin routes are protected at the middleware level
- **Email-based Authorization**: Only specified emails can access admin features
- **Server-side Validation**: All admin operations are validated server-side
- **Environment-based Config**: Admin emails are stored in environment variables

## Troubleshooting

### Can't Access Admin Panel
1. Check that your email is in the `ADMIN_EMAILS` environment variable
2. Make sure you have an account created with your admin email
3. Verify you're using the correct password
4. Check that the environment variable is properly formatted (comma-separated)
5. Try signing out and signing in again

### Data Not Loading
1. Check your Supabase connection
2. Verify the profiles table exists and has the correct structure
3. Check browser console for any JavaScript errors

### Export Not Working
1. Check that you have users selected (for Export Selected)
2. Verify your browser allows file downloads
3. Check browser console for any errors

## Technical Details

### File Structure
```
app/admin/
├── layout.tsx              # Admin layout with header
├── page.tsx                # Main admin page with stats and table
├── login/
│   └── page.tsx            # Admin login page
└── components/
    ├── admin-header.tsx    # Admin header with logout
    ├── user-table.tsx      # User management table
    ├── user-edit-modal.tsx # User editing modal
    └── admin-actions.tsx   # Bulk actions and export

lib/admin.ts                # Admin utilities and data fetching
lib/auth.ts                 # Authentication service (updated)
middleware.ts               # Route protection
```

### Key Functions
- `getAdminUsers()` - Fetch users with pagination and filtering
- `getAdminStats()` - Get dashboard statistics
- `updateUserContactStatus()` - Update contact status and notes
- `updateUserProfile()` - Update user profile data
- `exportUsersToCSV()` - Export users to CSV format

## Customization

### Adding New Contact Statuses
Edit the `CONTACT_STATUSES` array in `lib/admin.ts`:

```typescript
export const CONTACT_STATUSES = [
  { value: 'new', label: 'New', color: 'bg-blue-500' },
  { value: 'contacted', label: 'Contacted', color: 'bg-yellow-500' },
  // Add your custom statuses here
] as const
```

### Modifying Export Fields
Edit the `exportUsersToCSV()` function in `lib/admin.ts` to change which fields are exported.

### Styling
The admin interface uses the same design system as your main site:
- Dark theme with neon green accents
- 90-degree corners (rounded-none)
- Consistent typography and spacing
