import { supabase, type Profile, ProfileHelpers } from './supabase'

// Admin utilities and data fetching functions

export interface AdminUser {
  id: string
  email: string
  name: string | null
  phone: string | null
  country: string | null
  joinDate: string
  selectedProducts: Array<{
    name: string
    price: string
    selected_at: string
  }>
  purchases: Array<{
    product_name: string
    amount_cents: number
    status: string
    purchased_at: string
  }>
  contactStatus: string
  lastContacted: string | null
  notes: string | null
  totalSpent: number
  purchaseCount: number
  productCount: number
}

export interface AdminStats {
  totalUsers: number
  newUsers: number
  contactedUsers: number
  convertedUsers: number
  totalRevenue: number
}

/**
 * Check if email is admin (server-side safe)
 */
export function isEmailAdmin(email: string): boolean {
  const adminEmails = process.env.ADMIN_EMAILS?.split(',').map(email => email.trim()) || []
  return adminEmails.includes(email)
}

/**
 * Check if current user is admin (client-side)
 */
export async function isCurrentUserAdmin(): Promise<boolean> {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user?.email) return false

    return isEmailAdmin(user.email)
  } catch {
    return false
  }
}

/**
 * Get all users for admin table
 */
export async function getAdminUsers(
  page: number = 1,
  limit: number = 50,
  search?: string,
  contactFilter?: string,
  purchaseFilter?: string
): Promise<{ users: AdminUser[], total: number }> {
  try {
    let query = supabase
      .from('profiles')
      .select('*', { count: 'exact' })

    // Apply search filter
    if (search) {
      query = query.or(`email.ilike.%${search}%,name.ilike.%${search}%,phone.ilike.%${search}%`)
    }

    // Apply contact status filter
    if (contactFilter && contactFilter !== 'all') {
      query = query.eq('contact_info->status', contactFilter)
    }

    // Apply purchase filter
    if (purchaseFilter === 'has_purchases') {
      query = query.not('purchases', 'eq', '[]')
    } else if (purchaseFilter === 'no_purchases') {
      query = query.eq('purchases', '[]')
    }

    // Apply pagination
    const from = (page - 1) * limit
    const to = from + limit - 1
    query = query.range(from, to)

    // Order by created_at desc
    query = query.order('created_at', { ascending: false })

    const { data, error, count } = await query

    if (error) {
      console.error('Error fetching admin users:', error)
      return { users: [], total: 0 }
    }

    const users: AdminUser[] = (data || []).map(profile => ({
      id: profile.id,
      email: profile.email,
      name: profile.name,
      phone: profile.phone,
      country: profile.geo?.country || null,
      joinDate: profile.created_at,
      selectedProducts: profile.selected_products || [],
      purchases: profile.purchases || [],
      contactStatus: profile.contact_info?.status || 'new',
      lastContacted: profile.contact_info?.last_contacted_at || null,
      notes: profile.contact_info?.notes || null,
      totalSpent: (profile.purchases || []).reduce((sum, p) => sum + p.amount_cents, 0),
      purchaseCount: (profile.purchases || []).length,
      productCount: (profile.selected_products || []).length,
    }))

    return { users, total: count || 0 }
  } catch (error) {
    console.error('Error in getAdminUsers:', error)
    return { users: [], total: 0 }
  }
}

/**
 * Get admin dashboard stats
 */
export async function getAdminStats(): Promise<AdminStats> {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('contact_info, purchases, created_at')

    if (error) {
      console.error('Error fetching admin stats:', error)
      return {
        totalUsers: 0,
        newUsers: 0,
        contactedUsers: 0,
        convertedUsers: 0,
        totalRevenue: 0
      }
    }

    const now = new Date()
    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)

    const stats = data.reduce((acc, profile) => {
      acc.totalUsers++
      
      // New users (last 7 days)
      if (new Date(profile.created_at) > weekAgo) {
        acc.newUsers++
      }

      // Contact status
      const status = profile.contact_info?.status
      if (status === 'contacted' || status === 'converted') {
        acc.contactedUsers++
      }
      if (status === 'converted') {
        acc.convertedUsers++
      }

      // Revenue
      const purchases = profile.purchases || []
      acc.totalRevenue += purchases.reduce((sum, p) => sum + p.amount_cents, 0)

      return acc
    }, {
      totalUsers: 0,
      newUsers: 0,
      contactedUsers: 0,
      convertedUsers: 0,
      totalRevenue: 0
    })

    return stats
  } catch (error) {
    console.error('Error in getAdminStats:', error)
    return {
      totalUsers: 0,
      newUsers: 0,
      contactedUsers: 0,
      convertedUsers: 0,
      totalRevenue: 0
    }
  }
}

/**
 * Update user contact status
 */
export async function updateUserContactStatus(
  userId: string,
  status: string,
  notes?: string
): Promise<{ success: boolean, error?: string }> {
  try {
    const updates: any = {
      contact_info: {
        status,
        last_contacted_at: new Date().toISOString(),
        notes: notes || null
      }
    }

    const { error } = await supabase
      .from('profiles')
      .update(updates)
      .eq('id', userId)

    if (error) {
      return { success: false, error: error.message }
    }

    return { success: true }
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }
  }
}

/**
 * Update user profile data
 */
export async function updateUserProfile(
  userId: string,
  updates: Partial<Profile>
): Promise<{ success: boolean, error?: string }> {
  try {
    const { error } = await supabase
      .from('profiles')
      .update(updates)
      .eq('id', userId)

    if (error) {
      return { success: false, error: error.message }
    }

    return { success: true }
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }
  }
}

/**
 * Export users to CSV format
 */
export function exportUsersToCSV(users: AdminUser[]): string {
  const headers = [
    'Email',
    'Name',
    'Phone',
    'Country',
    'Join Date',
    'Contact Status',
    'Last Contacted',
    'Selected Products',
    'Purchase Count',
    'Total Spent',
    'Notes'
  ]

  const rows = users.map(user => [
    user.email,
    user.name || '',
    user.phone || '',
    user.country || '',
    new Date(user.joinDate).toLocaleDateString(),
    user.contactStatus,
    user.lastContacted ? new Date(user.lastContacted).toLocaleDateString() : '',
    user.selectedProducts.map(p => p.name).join('; '),
    user.purchaseCount.toString(),
    (user.totalSpent / 100).toFixed(2),
    user.notes || ''
  ])

  const csvContent = [headers, ...rows]
    .map(row => row.map(field => `"${field}"`).join(','))
    .join('\n')

  return csvContent
}

/**
 * Contact status options
 */
export const CONTACT_STATUSES = [
  { value: 'new', label: 'New', color: 'bg-blue-500' },
  { value: 'contacted', label: 'Contacted', color: 'bg-yellow-500' },
  { value: 'interested', label: 'Interested', color: 'bg-orange-500' },
  { value: 'converted', label: 'Converted', color: 'bg-green-500' },
  { value: 'not_interested', label: 'Not Interested', color: 'bg-gray-500' },
] as const

/**
 * Purchase status options
 */
export const PURCHASE_STATUSES = [
  { value: 'pending', label: 'Pending', color: 'bg-yellow-500' },
  { value: 'completed', label: 'Completed', color: 'bg-green-500' },
  { value: 'failed', label: 'Failed', color: 'bg-red-500' },
  { value: 'refunded', label: 'Refunded', color: 'bg-gray-500' },
] as const
