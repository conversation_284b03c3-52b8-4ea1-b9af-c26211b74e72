"use client"

import { supabase, ProfileHelpers, type ProfileInsert, type Profile, type SelectedProduct } from './supabase'
import type { User, AuthError } from '@supabase/supabase-js'

export interface AuthUser extends User {
  profile?: Profile | null
}

export interface AuthResult {
  user: AuthUser | null
  error: AuthError | null
}

/**
 * Authentication service for user registration and management
 */
export class AuthService {
  /**
   * Get current authenticated user with profile
   */
  static async getCurrentUser(): Promise<AuthUser | null> {
    try {
      const { data: { user }, error } = await supabase.auth.getUser()
      console.log(user)
      if (error || !user) {
        return null
      }

      // Fetch profile data
      const { data: profile } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single()

      return { ...user, profile }
    } catch (error) {
      // Silent error handling - errors are expected in normal flow
      return null
    }
  }

  /**
   * Add product to existing user profile
   */
  private static async addProductToUser(user: AuthU<PERSON>, selectedProduct: any): Promise<AuthResult> {
    if (!selectedProduct || !user.profile) {
      return { user, error: null }
    }

    const existingProducts = user.profile.selected_products || []
    const productExists = existingProducts.some(p => p.name === selectedProduct.name)

    if (productExists) {
      return { user, error: null }
    }

    const newProduct = {
      id: selectedProduct.name,
      name: selectedProduct.name,
      price: selectedProduct.price,
      price_cents: selectedProduct.price_cents || 0,
      currency: selectedProduct.currency || 'USD',
      selected_at: new Date().toISOString(),
      source: 'recurring_selection'
    }

    const updatedProducts = [...existingProducts, newProduct]
    const updateResult = await this.updateProfile(user.id, {
      selected_products: updatedProducts
    })

    if (updateResult.data) {
      return {
        user: { ...user, profile: updateResult.data },
        error: null
      }
    }

    return { user, error: null }
  }

  /**
   * Register a new user with email
   */
  static async signUpWithEmail(email: string, selectedProduct?: any): Promise<AuthResult> {
    try {
      // Check if user already exists and is logged in
      const existingUser = await this.getCurrentUser()
      if (existingUser?.email === email) {
        return await this.addProductToUser(existingUser, selectedProduct)
      }

      // Generate a secure temporary password that meets requirements
      const tempPassword = 'TempPass123!' + crypto.randomUUID().slice(0, 8)

      // Step 1: Create auth user with Supabase Auth
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email,
        password: tempPassword,
        options: {
          emailRedirectTo: undefined, // Disable email confirmation for now
          data: {
            email_confirm: false
          }
        }
      })

      if (authError) {

        // Check if user already exists
        if (authError.message?.includes('already registered') || authError.message?.includes('already been registered')) {
          console.log('User already exists but not logged in, trying to handle existing user...')

          // If a new product is selected, we should try to add it to the existing user
          if (selectedProduct) {
            console.log('🔄 Attempting to add product to existing user account')

            // Try to get the existing user profile by email
            const { data: existingProfile } = await supabase
              .from('profiles')
              .select('*')
              .eq('email', email)
              .single()

            if (existingProfile) {
              const existingProducts = existingProfile.selected_products || []

              // Check if this product is already selected
              const productExists = existingProducts.some((p: SelectedProduct) => p.name === selectedProduct.name)

              if (!productExists) {
                const newProduct = {
                  id: crypto.randomUUID(),
                  name: selectedProduct.name,
                  price: selectedProduct.price,
                  price_cents: selectedProduct.price_cents || 0,
                  currency: selectedProduct.currency || 'USD',
                  selected_at: new Date().toISOString(),
                  source: 'recurring_selection_existing_user'
                }

                const updatedProducts = [...existingProducts, newProduct]

                // Update the profile with the new product
                const { error: updateError } = await supabase
                  .from('profiles')
                  .update({ selected_products: updatedProducts })
                  .eq('email', email)

                if (!updateError) {
                  console.log('✅ New product added to existing user profile (not logged in)')
                  return {
                    user: null,
                    error: {
                      message: `Product "${selectedProduct.name}" has been added to your account. Please sign in to continue.`,
                      name: 'ProductAddedToExistingUser',
                      status: 200
                    } as AuthError
                  }
                }
              } else {
                console.log('ℹ️ Product already selected by existing user')
                return {
                  user: null,
                  error: {
                    message: `You've already selected "${selectedProduct.name}". Please sign in to continue.`,
                    name: 'ProductAlreadySelected',
                    status: 200
                  } as AuthError
                }
              }
            }
          }

          return {
            user: null,
            error: {
              message: 'User already exists. Please sign in to continue.',
              name: 'UserExistsError',
              status: 409
            } as AuthError
          }
        }

        return { user: null, error: authError }
      }

      if (!authData.user) {
        return {
          user: null,
          error: {
            message: 'Failed to create user account',
            name: 'SignupError',
            status: 400
          } as AuthError
        }
      }

      // Step 2: Create profile record in database
      console.log('🔄 Creating profile for user:', authData.user.id)

      // Prepare initial profile data
      let initialData: any = {}

      // Add selected product if provided
      if (selectedProduct) {
        console.log('🎯 Adding selected product to profile:', selectedProduct.name)
        initialData.selected_products = [{
          id: selectedProduct.name,
          name: selectedProduct.name,
          price: selectedProduct.price,
          price_cents: selectedProduct.price_cents || 0,
          currency: selectedProduct.currency || 'USD',
          selected_at: new Date().toISOString(),
          source: 'email_signup'
        }]
      }

      const profileData = ProfileHelpers.createProfile(email, authData.user.id, initialData)

      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .insert(profileData)
        .select()
        .single()

      if (profileError) {
        // Don't return error here - user is created, profile can be created later
        // Profile will be created on next login attempt
      }

      // Step 3: Return user with profile data
      const user: AuthUser = {
        ...authData.user,
        profile: profile || null
      }

      return { user, error: null }

    } catch (error) {
      return {
        user: null,
        error: {
          message: error instanceof Error ? error.message : 'Unknown error occurred',
          name: 'SignupError',
          status: 500
        } as AuthError 
      }
    }
  }



  /**
   * Update user profile with additional information
   */
  static async updateProfile(userId: string, updates: Partial<ProfileInsert>) {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', userId)
        .select()
        .single()

      if (error) {
        return { data: null, error }
      }
      return { data, error: null }
    } catch (error) {
      return {
        data: null,
        error: {
          message: error instanceof Error ? error.message : 'Unknown error occurred'
        }
      }
    }
  }

  /**
   * Sign in with email and password
   */
  static async signInWithPassword(email: string, password: string): Promise<AuthResult> {
    try {
      const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
        email,
        password
      })

      if (authError) {
        return {
          user: null,
          error: authError
        }
      }

      if (!authData.user) {
        return {
          user: null,
          error: {
            message: 'Failed to sign in',
            name: 'SignInError',
            status: 400
          } as AuthError
        }
      }

      // Fetch profile data
      const { data: profile } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', authData.user.id)
        .single()

      const user: AuthUser = {
        ...authData.user,
        profile: profile || null
      }

      return { user, error: null }
    } catch (error) {
      return {
        user: null,
        error: {
          message: error instanceof Error ? error.message : 'Unknown error occurred',
          name: 'SignInError',
          status: 500
        } as AuthError
      }
    }
  }

  /**
   * Sign out current user
   */
  static async signOut() {
    try {
      const { error } = await supabase.auth.signOut()
      if (error) {
        return { error }
      }
      return { error: null }
    } catch (error) {
      return { error }
    }
  }
}
