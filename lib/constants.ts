// App-wide constants

export const SITE_CONFIG = {
  name: "TradeForm",
  description: "",
  url: "https://tradeform.pro",
  logo: "/logo.png",
  ogImage: "/logo.png",
} as const

export const ANIMATION_CONFIG = {
  // Standard durations
  fast: 0.2,
  normal: 0.5,
  slow: 0.8,
  
  // Standard delays
  stagger: 0.1,
  section: 0.3,
  
  // Easing functions
  easeOut: "easeOut",
  easeInOut: "easeInOut",
  spring: { type: "spring", stiffness: 100, damping: 15 }
} as const

export const SCROLL_CONFIG = {
  behavior: 'smooth' as const,
  block: 'start' as const,
  inline: 'nearest' as const
}

export const BREAKPOINTS = {
  mobile: 768,
  tablet: 1024,
  desktop: 1280
} as const

export const FORM_CONFIG = {
  validation: {
    email: {
      required: "Email is required",
      pattern: {
        value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
        message: "Please enter a valid email address"
      }
    },
    phone: {
      required: "Phone number is required",
      minLength: {
        value: 10,
        message: "Please enter a valid phone number"
      }
    }
  },
  steps: {
    email: 1,
    phone: 2,
    details: 3
  }
} as const

export const COMMUNITY_COUNTER_CONFIG = {
  startValue: 2027,
  targetValue: 2033,
  duration: 60000, // 60 seconds
  intervals: [
    { at: 10000, increment: 1 }, // 10s
    { at: 25000, increment: 1 }, // 25s
    { at: 35000, increment: 1 }, // 35s
    { at: 45000, increment: 1 }, // 45s
    { at: 50000, increment: 1 }, // 50s
    { at: 55000, increment: 1 }  // 55s
  ]
} as const

export const SECTION_IDS = {
  hero: 'hero',
  beforeAfter: 'before-after',
  pricing: 'pricing',
  mentorStory: 'mentor-story',
  features: 'features',
  testimonials: 'testimonials',
  faq: 'faq',
  guarantee: 'guarantee',
  contact: 'contact'
} as const
