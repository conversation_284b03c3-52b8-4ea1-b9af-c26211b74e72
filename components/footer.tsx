"use client"

import { TypewriterText } from "./typewriter-text"
import { Logo } from "@/components/ui/logo"
import { motion } from "framer-motion";

export function Footer() {
  return (
    <footer id="footer" className="relative py-6 overflow-hidden">
      {/* Clean design - no background elements */}
      <div className="absolute top-0 inset-x-0 h-px bg-primary" />
      
      <div className="container mx-auto px-6 relative z-10">
        <motion.div 
          className="flex flex-col sm:flex-row justify-between items-center gap-3"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
        >
          <Logo
              size="xl"
              variant="icon-only"
              className="opacity-80"
          />
          <motion.div
            className="flex items-center space-x-3"
            whileHover={{ x: 3, transition: { duration: 0.2 } }}
          >

            <span className="font-mono text-sm text-primary opacity-80 tracking-wider">
              <TypewriterText text="TRADEFORM © TRADEFORM © TRADEFORM" speed={30} showCursor={false} />
            </span>
          </motion.div>
          
          <div className="flex items-center space-x-6">
            <motion.span 
              className="font-mono text-sm text-white/60 tracking-wider"
              whileHover={{ scale: 1.05, transition: { duration: 0.2 } }}
            >
              {new Date().getFullYear()}
            </motion.span>
          </div>
        </motion.div>
        <div className="container mt-6">
          <div className="typography-body-s text-center text-xs  text-gray-600">
            The foreign exchange market and currency trading is highly speculative in nature and as such, currency prices may become extremely volatile. You may sustain a total loss of your funds. Foreign exchange trading carries a high level of risk and may not be suitable for all investors. Always consult a financial professional before making any investment decisions. It is possible you will sustain a loss of some or all of your initial investment. No representation is being made that any account will or is likely to achieve profits or losses. Past performance is not indicative of future results. Individual results vary and no representation is made that you will or are likely to achieve profits or incur losses comparable to those that may be shown. You acknowledge and agree that no promise or guarantee of success or profitability has been made between you, and TradeForm. For further information, please review our Terms of Use, Privacy Policy, and Risk Disclosures. CFTC RULE 4.41- Hypothetical or simulated performance results have certain limitations. Unlike an actual performance record, simulated results do not represent actual trading. Also, since the trades have not been executed, the results may have under-or-over compensated for the impact, if any, of certain market factors, including but not limited to, lack of liquidity. Simulated trading programs in general, are also subject to the fact that they are designed with the benefit of hindsight. No representation is being made that any account will or is likely to achieve profit or losses similar to those depicted on the tradeform.pro
            website. 2025 © All rights reserved. TradeForm
          </div>
        </div>
      </div>
    </footer>
  )
}
