import React from "react"
import Image from "next/image"
import { cn } from "@/lib/utils"
import { SITE_CONFIG } from "@/lib/constants"

interface LogoProps {
  size?: "sm" | "md" | "lg" | "xl"
  variant?: "default" | "icon-only" | "text-only"
  className?: string
  showText?: boolean
  textClassName?: string
}

const sizeClasses = {
  sm: "h-6 w-6",
  md: "h-8 w-8", 
  lg: "h-10 w-10",
  xl: "h-12 w-12"
}

const textSizeClasses = {
  sm: "text-sm",
  md: "text-base",
  lg: "text-lg", 
  xl: "text-xl"
}

export function Logo({ 
  size = "md", 
  variant = "default",
  className,
  showText = true,
  textClassName
}: LogoProps) {
  const logoImage = (
    <Image
      src={SITE_CONFIG.logo}
      alt={`${SITE_CONFIG.name} Logo`}
      width={48}
      height={48}
      className={cn(sizeClasses[size], className)}
      priority
    />
  )

  const logoText = showText && variant !== "icon-only" && (
    <span className={cn(
      "font-mono font-bold",
      textSizeClasses[size],
      textClassName
    )}>
      <span className="text-white">Trade</span>
      <span className="text-primary">Form</span>
    </span>
  )

  if (variant === "text-only") {
    return logoText
  }

  if (variant === "icon-only") {
    return logoImage
  }

  return (
    <div className="flex items-center gap-1">
      {logoImage}
      {logoText}
    </div>
  )
}
