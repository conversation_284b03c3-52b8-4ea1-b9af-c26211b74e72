"use client"

import React, { useState } from "react"
import { motion } from "framer-motion"
import { ChevronDown, MessageSquare } from "lucide-react"
import { CommunityCounter } from "./community-counter"
import { faqItems as baseFaqItems } from "@/lib/content-data"
import { fadeInUp } from "@/lib/animations"

export function FaqSection() {
  const [openIndex, setOpenIndex] = useState<number | null>(0) // Start with first FAQ open

  // Enhance FAQ items with community counter for the community question
  const faqItems = baseFaqItems.map((item, index) => {
    if (item.question.toLowerCase().includes("community")) {
      return {
        ...item,
        answer: (
          <>
            Yes! You'll join our private community of{" "}
            <CommunityCounter
              className="font-semibold"
              suffix="+"
              formatNumber={true}
            />{" "}
            members. This includes daily market discussions, trade reviews, strategy sharing, and regular live events. Many members cite the community as a key factor in their success.
          </>
        )
      }
    }
    return item
  })


  const toggleFaq = (index: number) => {
    setOpenIndex(openIndex === index ? null : index)
  }

  return (
    <div className="py-24 relative overflow-hidden bg-black">
      {/* Clean design - no background elements */}

      <div className="container mx-auto px-6 relative z-10">
        <motion.div
          {...fadeInUp}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="typography-h2 mb-4 tracking-tight text-white">
            People <span className="text-primary">Often Aks</span>
          </h2>
        </motion.div>

        <div className="max-w-3xl mx-auto">
          {faqItems.map((item, index) => {
            const isOpen = openIndex === index;

            return (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="mb-6"
              >
                <motion.button
                  onClick={() => toggleFaq(index)}
                  className={`w-full text-left p-6 bg-black flex justify-between items-center transition-all duration-300 group border ${
                    isOpen
                      ? "border-primary/60 shadow-sm"
                      : "border-primary/30 hover:border-primary/60 hover:shadow-sm"
                  }`}
                  whileHover={{ scale: 1.005 }}
                  whileTap={{ scale: 0.995 }}
                >
                  <h3 className={`typography-body-l font-semibold transition-colors duration-300 text-white`}>
                    {item.question}
                  </h3>
                  <motion.div
                    className={`w-8 h-8 flex items-center justify-center transition-colors duration-300 border ${
                      isOpen
                        ? "border-primary/60"
                        : "border-primary/30 group-hover:border-primary/60"
                    }`}
                    animate={{ rotate: isOpen ? 180 : 0 }}
                    transition={{ duration: 0.3, ease: "easeInOut" }}
                  >
                    <ChevronDown
                      className={`w-4 h-4 transition-colors duration-300 ${
                        isOpen ? 'text-primary' : 'text-white/60 group-hover:text-primary/60'
                      }`}
                    />
                  </motion.div>
                </motion.button>

                <motion.div
                  initial={false}
                  animate={{
                    height: isOpen ? "auto" : 0,
                    opacity: isOpen ? 1 : 0,
                  }}
                  transition={{
                    height: {
                      duration: 0.6,
                      ease: [0.25, 0.46, 0.45, 0.94], // easeOutQuart
                    },
                    opacity: {
                      duration: 0.4,
                      delay: isOpen ? 0.2 : 0,
                      ease: "easeInOut"
                    }
                  }}
                  className="overflow-hidden"
                >
                  <motion.div
                    initial={false}
                    animate={{
                      y: isOpen ? 0 : -20,
                    }}
                    transition={{
                      duration: 0.5,
                      delay: isOpen ? 0.15 : 0,
                      ease: [0.25, 0.46, 0.45, 0.94]
                    }}
                    className="bg-black px-6 pb-6 border-x border-b border-primary/30"
                  >
                    <div className="typography-body-s text-white/80 pt-4 leading-relaxed">
                      {item.answer}
                    </div>
                  </motion.div>
                </motion.div>
              </motion.div>
            );
          })}
        </div>
        
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
          className="text-center mt-12"
        >
          <motion.div
            whileHover={{ y: -3, transition: { duration: 0.2 } }}
            className="inline-flex items-center gap-2 px-6 py-3 bg-black border border-primary/30 hover:border-primary/60 transition-all duration-300"
          >
            <MessageSquare className="w-4 h-4 text-primary" />
            <span className="font-mono text-white uppercase">
              Have more questions? <a href="#contact" className="font-semibold text-primary hover:text-primary/80 transition-colors duration-300">Contact us</a>
            </span>
          </motion.div>
        </motion.div>
      </div>
    </div>
  )
}
