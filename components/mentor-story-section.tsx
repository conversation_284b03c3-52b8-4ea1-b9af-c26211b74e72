"use client"

import { motion } from "framer-motion"
import Image from "next/image"
import { fadeInUp } from "@/lib/animations"
import {ScrollToButton} from "@/components/common/scroll-to-button";
import {SECTION_IDS} from "@/lib/constants";
import {ArrowRight} from "lucide-react";
import {ANALYTICS_EVENTS} from "@/lib/analytics/events";
import {useAnalytics} from "@/lib/analytics/hooks";

interface StoryStep {
  image: string
  chapter: string
  title: string
  description: string
  stats: string
  year: string
}

const storySteps: StoryStep[] = [
  {
    image: "/mentor-story/IMG_0570.jpg",
    chapter: "Chapter 1",
    title: "The Beginning",
    description: "Started with a simple goal: financial freedom. Through disciplined strategy and relentless learning, I transformed my approach to trading.",
    stats: "Multi-millionaire portfolio",
    year: "2019-2023"
  },
  {
    image: "/mentor-crowd.jpg",
    chapter: "Chapter 2",
    title: "Sharing Knowledge",
    description: "Success isn't just personal. I began teaching others, creating the Swing Trading Lab. Watching students achieve their dreams became my new passion.",
    stats: "$3M+ combined student earnings",
    year: "2021-2024"
  },
  {
    image: "/mentor-story/3.jpg",
    chapter: "Chapter 3",
    title: "Proving the System",
    description: "To demonstrate the power of the strategy, I took on the ultimate challenge: turning a small account into life-changing wealth.",
    stats: "$100 → $1,000,000",
    year: "4 months"
  }
]

export function MentorStorySection() {
  const { track } = useAnalytics()

  const handleCtaClick = async () => {
    const properties = {
      cta_text: 'Start Your Journey',
      target_section: SECTION_IDS.mentorStory,
      position: 'mentor-story',
      timestamp: new Date().toISOString(),
    }

    await track(ANALYTICS_EVENTS.hero_cta_click, properties)
  }

  return (
    <div className="py-24 md:py-32 relative overflow-hidden">
      {/* Minimal background - no distracting elements */}

      <div className="container mx-auto px-6 relative z-10 max-w-4xl">
        <motion.div
          {...fadeInUp}
          viewport={{ once: true }}
          className="text-center mb-20"
        >
          <h2 className="typography-h2 mb-6 tracking-tight text-center">
            This is <span className="text-primary">My Story</span>
          </h2>
          <p className="typography-body-l text-white/70 max-w-2xl mx-auto">
            From struggling trader to mentor of thousands. Here's how I built a system that changed everything.
          </p>
        </motion.div>

        {/* Story Timeline */}
        <div className="space-y-24 md:space-y-32">
          {storySteps.map((step, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 60 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{
                duration: 0.8,
                delay: index * 0.2,
                ease: "easeOut"
              }}
              viewport={{ once: true, margin: "-50px" }}
              className="relative"
            >
              {/* Timeline connector - hidden on mobile */}
              {index < storySteps.length - 1 && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  whileInView={{ height: "6rem", opacity: 1 }}
                  transition={{ duration: 1.2, delay: 0.8 + index * 0.3 }}
                  viewport={{ once: true }}
                  className="absolute left-1/2 -translate-x-0.5 bottom-0 w-0.5 bg-gradient-to-b from-primary/80 via-primary/40 to-transparent z-10"
                  style={{ top: "100%" }}
                />
              )}

              <div className={`flex flex-col md:flex-row items-center gap-8 md:gap-12 ${
                index % 2 === 1 ? 'md:flex-row-reverse' : ''
              }`}>
                {/* Image Section - Enhanced for mobile */}
                <motion.div
                    initial={{ opacity: 0, x: index % 2 === 0 ? -30 : 30 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.8, delay: 0.3 }}
                    viewport={{ once: true }}
                    className="w-1/2 relative groupm mb-4 hidden md:block"
                >
                  <div className="relative aspect-[16/10] md:aspect-[4/3] overflow-hidden border border-primary/30 bg-black hover:border-primary/60 transition-all duration-500 group">
                    <Image
                        src={step.image}
                        alt={step.title}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-700"
                        sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 50vw"
                        priority={index === 0}
                    />
                    <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-all duration-500" />

                    {/* Floating stats overlay - better positioned for mobile */}
                    <motion.div
                        initial={{ opacity: 0, scale: 0.8 }}
                        whileInView={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.6, delay: 0.8 }}
                        viewport={{ once: true }}
                        className="absolute top-4 right-4 md:top-6 md:right-6"
                    >
                      <div className="bg-black/90 border border-primary/60 px-3 py-1.5 md:px-4 md:py-2 backdrop-blur-sm">
                        <span className="typography-body-s text-primary font-mono font-bold text-sm md:text-base">
                          {step.year}
                        </span>
                      </div>
                    </motion.div>
                  </div>
                </motion.div>

                {/* Content Section - Enhanced for mobile */}
                <motion.div
                  initial={{ opacity: 0, x: index % 2 === 0 ? 30 : -30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 0.5 }}
                  viewport={{ once: true }}
                  className="w-full md:w-1/2 text-center md:text-left px-4 md:px-0"
                >
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.7 }}
                    viewport={{ once: true }}
                    className="inline-block mb-4 px-3 py-1 md:px-4 md:py-1.5 border border-primary/40 bg-primary/10 backdrop-blur-sm"
                  >
                    <span className="typography-body-s text-primary font-medium uppercase tracking-wider text-sm md:text-base">
                      {step.chapter}
                    </span>
                  </motion.div>
                  <motion.div
                      initial={{ opacity: 0, x: index % 2 === 0 ? -30 : 30 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.8, delay: 0.3 }}
                      viewport={{ once: true }}
                      className=" max-w-lg mx-auto relative groupm mb-4 md:hidden "
                  >
                    <div className="relative aspect-[16/10] md:aspect-[4/3] overflow-hidden border border-primary/30 bg-black hover:border-primary/60 transition-all duration-500 group">
                      <Image
                          src={step.image}
                          alt={step.title}
                          fill
                          className="object-cover group-hover:scale-105 transition-transform duration-700"
                          sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 50vw"
                          priority={index === 0}
                      />
                      <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-all duration-500" />

                      {/* Floating stats overlay - better positioned for mobile */}
                      <motion.div
                          initial={{ opacity: 0, scale: 0.8 }}
                          whileInView={{ opacity: 1, scale: 1 }}
                          transition={{ duration: 0.6, delay: 0.8 }}
                          viewport={{ once: true }}
                          className="absolute top-4 right-4 md:top-6 md:right-6"
                      >
                        <div className="bg-black/90 border border-primary/60 px-3 py-1.5 md:px-4 md:py-2 backdrop-blur-sm">
                        <span className="typography-body-s text-primary font-mono font-bold text-sm md:text-base">
                          {step.year}
                        </span>
                        </div>
                      </motion.div>
                    </div>
                  </motion.div>
                  <motion.h3
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.8 }}
                    viewport={{ once: true }}
                    className="text-2xl md:text-3xl lg:text-4xl font-bold mb-4 md:mb-6 text-white leading-tight"
                  >
                    {step.title}
                  </motion.h3>

                  <motion.p
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.9 }}
                    viewport={{ once: true }}
                    className="text-base md:text-lg text-white/80 mb-6 md:mb-8 leading-relaxed max-w-lg mx-auto lg:mx-0"
                  >
                    {step.description}
                  </motion.p>

                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.6, delay: 1 }}
                    viewport={{ once: true }}
                    className="inline-block bg-black border border-primary/50 px-4 py-3 md:px-6 md:py-4 hover:border-primary/80 transition-all duration-300"
                  >
                    <span className="text-lg md:text-xl font-mono font-bold text-primary">
                      {step.stats}
                    </span>
                  </motion.div>
                </motion.div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Call to Action - Mobile optimized */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center mt-20 md:mt-24"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            viewport={{ once: true }}
            className="bg-gradient-to-r from-black via-primary/5 to-black border border-primary/30 p-6 md:p-8 lg:p-12 backdrop-blur-sm"
          >
            <h3 className="text-2xl md:text-3xl lg:text-4xl font-bold mb-4 md:mb-6 text-white leading-tight">
              Your Story <span className="text-primary">Starts Here</span>
            </h3>
            <p className="text-base md:text-lg text-white/80 max-w-4xl mx-auto mb-6 md:mb-8 leading-relaxed">
              This isn't just my story—it's proof of what's possible. The same strategies, the same system,
              the same potential for transformation. Your chapter begins now.
            </p>

            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="inline-block"
            >
              <ScrollToButton
                  targetId={SECTION_IDS.pricing}
                  variant="default"
                  size="default"
                  className="gap-2"
                  onClick={handleCtaClick}
              >
                Start Your Journey
                <ArrowRight className="w-4 h-4" />
              </ScrollToButton>
            </motion.div>
          </motion.div>
        </motion.div>
      </div>
    </div>
  )
}
