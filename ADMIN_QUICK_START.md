# Admin System - Quick Start Guide

## 🚀 Get Started in 3 Steps

### Step 1: Set Admin Emails
Add this to your `.env.local` file:
```bash
ADMIN_EMAILS=<EMAIL>,<EMAIL>
```

### Step 2: Create Admin Account
You need an account with your admin email. Choose one option:

**Option A: Use your main site signup**
1. Go to your main site
2. Use the signup modal with your admin email
3. Complete the signup process

**Option B: Use Supabase Dashboard**
1. Go to your Supabase project
2. Authentication > Users > Add user
3. Create account with your admin email

### Step 3: Access Admin Panel
1. Go to `/admin` in your browser
2. Sign in with your admin email and password
3. Start managing users!

## 🔑 Login Process

1. **Visit `/admin`** - You'll be redirected to login if not authenticated
2. **Enter credentials** - Use your admin email and password
3. **Access granted** - Only emails in `ADMIN_EMAILS` can access

## 📊 What You Can Do

- **View all users** with their contact info and purchases
- **Search and filter** users by status, purchases, etc.
- **Update contact status** (New → Contacted → Converted)
- **Add admin notes** for each user
- **Bulk update** multiple users at once
- **Export data** to CSV for external use
- **Edit user information** in detailed modal

## 🛡️ Security Features

- **Email-based authorization** - Only specified emails can access
- **Middleware protection** - Routes protected at server level
- **Secure authentication** - Uses Supabase Auth
- **Admin-only access** - Regular users cannot access admin features

## 🔧 Quick Troubleshooting

**Can't access admin?**
- Check your email is in `ADMIN_EMAILS`
- Make sure you have an account created
- Verify correct password
- Try signing out and back in

**Data not loading?**
- Check Supabase connection
- Verify profiles table exists
- Check browser console for errors

## 📱 Mobile Friendly

The admin interface works on mobile devices with:
- Responsive table design
- Touch-friendly buttons
- Mobile-optimized modals
- Collapsible columns

---

**Need help?** Check the full documentation in `ADMIN_SETUP.md`
